#!/bin/bash
#
# Test script to validate configuration and Image Builder availability
#

set -e

echo "=== OpenWrt Build Configuration Test ==="
echo

# Configuration
REPO_NAME="immortalwrt/immortalwrt"
BUILD_VER="24.10"
TARGET="x86"
SUBTARGET="64"
CONFIG_FILE=".config"

echo "Repository: $REPO_NAME"
echo "Build Version: $BUILD_VER"
echo "Target: $TARGET/$SUBTARGET"
echo

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Error: $CONFIG_FILE not found!"
    exit 1
fi
echo "✅ Config file found: $CONFIG_FILE"

# Get latest tag
echo
echo "🔍 Checking latest tag..."
REPO_TAG=$(curl -s "https://api.github.com/repos/$REPO_NAME/tags" | grep -o '"name": *"[^"]*"' | head -1 | cut -d'"' -f4)
if [ -z "$REPO_TAG" ]; then
    echo "❌ Failed to get latest tag"
    exit 1
fi
echo "✅ Latest tag: $REPO_TAG"

# Check Image Builder availability
echo
echo "🔍 Checking Image Builder availability..."
IB_URL="https://downloads.immortalwrt.org/releases/$BUILD_VER/targets/$TARGET/$SUBTARGET/immortalwrt-imagebuilder-$REPO_TAG-$TARGET-$SUBTARGET.Linux-x86_64.tar.xz"
echo "Image Builder URL: $IB_URL"

if curl -s --head "$IB_URL" | head -n 1 | grep -q "200 OK"; then
    echo "✅ Image Builder available"
    IB_AVAILABLE=true
else
    echo "⚠️  Image Builder not available, will use source build"
    IB_AVAILABLE=false
fi

# Test package generation script
echo
echo "🔍 Testing package generation..."
if [ -f "generate-packages.sh" ]; then
    chmod +x generate-packages.sh
    if ./generate-packages.sh "$CONFIG_FILE" test-packages.txt; then
        echo "✅ Package generation successful"
        PACKAGE_COUNT=$(wc -w < test-packages.txt)
        echo "📦 Generated $PACKAGE_COUNT packages"
        echo "First 10 packages:"
        head -c 200 test-packages.txt | tr ' ' '\n' | head -10 | sed 's/^/  - /'
        rm -f test-packages.txt
    else
        echo "❌ Package generation failed"
    fi
else
    echo "❌ generate-packages.sh not found"
fi

# Check required scripts
echo
echo "🔍 Checking required scripts..."
SCRIPTS=("diy-part1.sh" "diy-part2.sh" "diy-imagebuilder.sh")
for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "✅ $script found"
    else
        echo "❌ $script missing"
    fi
done

# Analyze config file
echo
echo "📊 Config file analysis:"
TOTAL_PACKAGES=$(grep -c "^CONFIG_PACKAGE_.*=y$" "$CONFIG_FILE" || echo "0")
KERNEL_MODULES=$(grep -c "^CONFIG_PACKAGE_kmod-.*=y$" "$CONFIG_FILE" || echo "0")
LUCI_APPS=$(grep -c "^CONFIG_PACKAGE_luci-app-.*=y$" "$CONFIG_FILE" || echo "0")
THEMES=$(grep -c "^CONFIG_PACKAGE_luci-theme-.*=y$" "$CONFIG_FILE" || echo "0")

echo "  - Total packages: $TOTAL_PACKAGES"
echo "  - Kernel modules: $KERNEL_MODULES"
echo "  - LuCI apps: $LUCI_APPS"
echo "  - Themes: $THEMES"

# Check target configuration
echo
echo "🎯 Target configuration:"
TARGET_ARCH=$(grep "^CONFIG_TARGET_" "$CONFIG_FILE" | head -1)
if [ -n "$TARGET_ARCH" ]; then
    echo "✅ Target architecture: $TARGET_ARCH"
else
    echo "❌ No target architecture found"
fi

# Summary
echo
echo "=== Summary ==="
if [ "$IB_AVAILABLE" = true ]; then
    echo "🚀 Recommended build method: Image Builder (fast)"
    echo "⏱️  Estimated build time: 5-15 minutes"
else
    echo "🔨 Required build method: Source compilation"
    echo "⏱️  Estimated build time: 1-3 hours"
fi

echo
echo "✅ Configuration test completed!"
echo
echo "To start a build:"
echo "1. Go to GitHub Actions"
echo "2. Select 'Build OpenWrt (Image Builder)' workflow"
echo "3. Click 'Run workflow'"
if [ "$IB_AVAILABLE" = false ]; then
    echo "4. Enable 'Use full build' option"
fi
