#!/bin/bash
#
# Script to generate package list for Image Builder from .config file
# This script extracts package selections and formats them for Image Builder
#

CONFIG_FILE="${1:-.config}"
OUTPUT_FILE="${2:-packages.txt}"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file $CONFIG_FILE not found!"
    exit 1
fi

echo "Generating package list from $CONFIG_FILE..."

# Initialize packages list
PACKAGES=""
EXCLUDED_PACKAGES=""

# Read .config and extract package selections
while IFS= read -r line; do
    # Handle enabled packages (CONFIG_PACKAGE_xxx=y)
    if [[ $line =~ ^CONFIG_PACKAGE_(.+)=y$ ]]; then
        pkg_name="${BASH_REMATCH[1]}"
        
        # Skip certain packages that are handled automatically by Image Builder
        case "$pkg_name" in
            # Skip kernel modules - they're usually included automatically
            kmod-*)
                continue
                ;;
            # Skip base system packages
            base-files|busybox|ca-certificates|dropbear|firewall*|fstools|libc|libgcc*|kernel|mtd)
                continue
                ;;
            # Skip some lib packages that are dependencies
            libuci*|libiwinfo*|libpthread*|librt*|libm|libdl)
                continue
                ;;
            # Include everything else
            *)
                PACKAGES="$PACKAGES $pkg_name"
                ;;
        esac
    
    # Handle disabled packages (CONFIG_PACKAGE_xxx=n or # CONFIG_PACKAGE_xxx is not set)
    elif [[ $line =~ ^#\ CONFIG_PACKAGE_(.+)\ is\ not\ set$ ]] || [[ $line =~ ^CONFIG_PACKAGE_(.+)=n$ ]]; then
        pkg_name="${BASH_REMATCH[1]}"
        # Add to excluded packages with minus prefix
        EXCLUDED_PACKAGES="$EXCLUDED_PACKAGES -$pkg_name"
    fi
done < "$CONFIG_FILE"

# Add essential packages that should always be included
ESSENTIAL_PACKAGES="luci luci-ssl-openssl"

# Combine all packages
ALL_PACKAGES="$ESSENTIAL_PACKAGES $PACKAGES $EXCLUDED_PACKAGES"

# Clean up extra spaces
ALL_PACKAGES=$(echo $ALL_PACKAGES | tr -s ' ')

echo "Generated package list:"
echo "$ALL_PACKAGES"

# Save to output file
echo "$ALL_PACKAGES" > "$OUTPUT_FILE"
echo "Package list saved to $OUTPUT_FILE"

# Also output for GitHub Actions
if [ -n "$GITHUB_ENV" ]; then
    echo "PACKAGES=$ALL_PACKAGES" >> $GITHUB_ENV
fi
