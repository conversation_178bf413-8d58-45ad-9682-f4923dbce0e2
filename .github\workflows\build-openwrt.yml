#
# Copyright (c) 2019-2020 P3TERX <https://p3terx.com>
#
# This is free software, licensed under the MIT License.
# See /LICENSE for more information.
#
# https://github.com/P3TERX/Actions-OpenWrt
# Description: Build OpenWrt using GitHub Actions
#2023.06.24-2325

name: Build OpenWrt

on:
  repository_dispatch:
  workflow_dispatch:
    inputs:
      clean_cache:
        description: 'Clean cache'
        required: false
        default: "false"
      use_imagebuilder:
        description: 'Use Image Builder (faster)'
        required: false
        default: "true"

env:
  REPO_NAME: immortalwrt/immortalwrt
  BUILD_VER: "24.10"
  TARGET: x86
  SUBTARGET: 64
  PROFILE: generic
  FEEDS_CONF: feeds.conf.default
  CONFIG_FILE: .config
  DIY_P1_SH: diy-part1.sh
  DIY_P2_SH: diy-part2.sh
  UPLOAD_BIN_DIR: false
  UPLOAD_FIRMWARE: true
  UPLOAD_RELEASE: true
  TZ: Asia/Shanghai

jobs:
  build:
    runs-on: ubuntu-22.04
    permissions:
      actions: write
      contents: write

    steps:

    - name: Initialization environment
      env:
        DEBIAN_FRONTEND: noninteractive
      run: |
        sudo rm -rf /etc/apt/sources.list.d/* /usr/share/dotnet /usr/local/lib/android /etc/mysql /etc/php /opt/ghc /opt/hostedtoolcache/CodeQL
        sudo rm -rf /usr/local/share/boost /usr/local/graalvm /usr/local/share/powershell /usr/local/share/chromium
        sudo rm -rf /usr/local/lib/node_modules /opt/az /opt/microsoft /usr/share/swift
        sudo docker image prune --all --force
        sudo apt-get clean
        sudo -E apt-get -qq update
        sudo -E apt-get -qq install ack antlr3 asciidoc autoconf automake autopoint binutils bison build-essential \
          bzip2 ccache clang cmake cpio curl device-tree-compiler ecj fastjar flex gawk gettext gcc-multilib \
          g++-multilib git gnutls-dev gperf haveged help2man intltool lib32gcc-s1 libc6-dev-i386 libelf-dev \
          libglib2.0-dev libgmp3-dev libltdl-dev libmpc-dev libmpfr-dev libncurses5-dev libncursesw5 \
          libncursesw5-dev libpython3-dev libreadline-dev libssl-dev libtool lld llvm lrzsz mkisofs msmtp \
          nano ninja-build p7zip p7zip-full patch pkgconf python2.7 python3 python3-pip python3-ply \
          python3-docutils python3-pyelftools qemu-utils re2c rsync scons squashfs-tools subversion swig \
          texinfo uglifyjs upx-ucl unzip vim wget xmlto xxd zlib1g-dev
        sudo timedatectl set-timezone "$TZ"
        sudo mkdir -p /workdir
        sudo chown $USER:$GROUPS /workdir

    - name: Checkout
      uses: actions/checkout@main

    - name: Get latest tag and determine build method
      run: |
        REPO_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$REPO_NAME/tags" | awk -F '"' '/name/{print $4}' | grep $BUILD_VER | head -n 1)
        echo "REPO_TAG=$REPO_TAG" >> $GITHUB_ENV
        echo "Build Version: $REPO_TAG"

        # Check if Image Builder should be used
        if [ "${{ github.event.inputs.use_imagebuilder }}" == "true" ]; then
          # Check if Image Builder is available
          IB_URL="https://downloads.immortalwrt.org/releases/$BUILD_VER/targets/$TARGET/$SUBTARGET/immortalwrt-imagebuilder-$REPO_TAG-$TARGET-$SUBTARGET.Linux-x86_64.tar.xz"
          if curl -s --head "$IB_URL" | head -n 1 | grep -q "200 OK"; then
            echo "USE_IMAGEBUILDER=true" >> $GITHUB_ENV
            echo "Using Image Builder for faster build"
          else
            echo "USE_IMAGEBUILDER=false" >> $GITHUB_ENV
            echo "Image Builder not available, using source build"
          fi
        else
          echo "USE_IMAGEBUILDER=false" >> $GITHUB_ENV
          echo "Using source build as requested"
        fi

    - name: Download and setup Image Builder
      if: env.USE_IMAGEBUILDER == 'true'
      working-directory: /workdir
      run: |
        df -hT $PWD
        IB_URL="https://downloads.immortalwrt.org/releases/$BUILD_VER/targets/$TARGET/$SUBTARGET/immortalwrt-imagebuilder-$REPO_TAG-$TARGET-$SUBTARGET.Linux-x86_64.tar.xz"
        echo "Downloading Image Builder from: $IB_URL"
        wget "$IB_URL" -O imagebuilder.tar.xz
        tar -xf imagebuilder.tar.xz
        mv immortalwrt-imagebuilder-* imagebuilder
        ln -sf /workdir/imagebuilder $GITHUB_WORKSPACE/imagebuilder
        echo "Image Builder setup completed"
        df -hT

    - name: Clone source code
      if: env.USE_IMAGEBUILDER != 'true'
      working-directory: /workdir
      run: |
        df -hT $PWD
        REPO_URL="https://github.com/$REPO_NAME.git"
        echo "Cloning source code for tag: $REPO_TAG"
        git clone $REPO_URL -b $REPO_TAG --depth=1 openwrt
        ln -sf /workdir/openwrt $GITHUB_WORKSPACE/openwrt
        echo "After clone:"
        df -hT

    - name: Cache
      if: env.USE_IMAGEBUILDER != 'true'
      uses: klever1988/cachewrtbuild@main
      with:
        ccache: 'true'
        prefix: ${{ github.workspace }}/openwrt
        clean: ${{ github.event.inputs.clean_cache }}

    - name: Load custom feeds
      if: env.USE_IMAGEBUILDER != 'true'
      run: |
        [ -e $FEEDS_CONF ] && mv $FEEDS_CONF openwrt/feeds.conf.default
        chmod +x $DIY_P1_SH
        cd openwrt
        $GITHUB_WORKSPACE/$DIY_P1_SH

    - name: Update feeds
      if: env.USE_IMAGEBUILDER != 'true'
      run: cd openwrt && ./scripts/feeds update -a

    - name: Install feeds
      if: env.USE_IMAGEBUILDER != 'true'
      run: cd openwrt && ./scripts/feeds install -a

    - name: Re Install feeds
      if: env.USE_IMAGEBUILDER != 'true'
      run: cd openwrt && ./scripts/feeds install -a

    - name: Add Github release Tag to firmware
      run: |
        BUILD_DATE=$(date +"%Y.%m.%d")
        echo "RELEASE_TAG=${{ env.REPO_TAG }}-$BUILD_DATE" >> $GITHUB_ENV
        if [ "$USE_IMAGEBUILDER" != "true" ]; then
          cd openwrt
          echo "DISTRIB_GITHUBVER='${{ env.REPO_TAG }}-$BUILD_DATE'" >> package/base-files/files/etc/openwrt_release
        fi

    - name: Load custom configuration (source build)
      if: env.USE_IMAGEBUILDER != 'true'
      run: |
        [ -e files ] && mv files openwrt/files
        [ -e $CONFIG_FILE ] && mv $CONFIG_FILE openwrt/.config
        chmod +x $DIY_P2_SH
        cd openwrt
        $GITHUB_WORKSPACE/$DIY_P2_SH

    - name: Download package (source build)
      if: env.USE_IMAGEBUILDER != 'true'
      id: package
      run: |
        cd openwrt
        make defconfig
        make download -j8 V=s
        find dl -size -1024c -exec ls -l {} \;
        find dl -size -1024c -exec rm -f {} \;

    - name: Prepare Image Builder
      if: env.USE_IMAGEBUILDER == 'true'
      run: |
        cd imagebuilder

        # Extract packages from .config
        PACKAGES=""
        while IFS= read -r line; do
          if [[ $line =~ ^CONFIG_PACKAGE_(.+)=y$ ]]; then
            pkg_name="${BASH_REMATCH[1]}"
            # Skip kernel modules and base packages
            if [[ ! $pkg_name =~ ^kmod- ]] && [[ ! $pkg_name =~ ^lib ]] && [[ $pkg_name != "base-files" ]]; then
              PACKAGES="$PACKAGES $pkg_name"
            fi
          fi
        done < "$GITHUB_WORKSPACE/$CONFIG_FILE"

        # Add essential packages
        PACKAGES="luci luci-ssl-openssl $PACKAGES"
        echo "PACKAGES=$PACKAGES" >> $GITHUB_ENV
        echo "Selected packages: $PACKAGES"

        # Copy custom files
        if [ -d "$GITHUB_WORKSPACE/files" ]; then
          echo "Copying custom files..."
          mkdir -p files
          cp -r "$GITHUB_WORKSPACE/files"/* files/ 2>/dev/null || true
        fi

        # Apply diy-part2.sh customizations to files
        if [ -f "$GITHUB_WORKSPACE/$DIY_P2_SH" ]; then
          echo "Applying DIY customizations..."
          chmod +x "$GITHUB_WORKSPACE/$DIY_P2_SH"
          # Create a temporary script to apply customizations to files
          mkdir -p files/etc/config
          # Apply network IP change from diy-part2.sh
          if grep -q "*************" "$GITHUB_WORKSPACE/$DIY_P2_SH"; then
            echo "Applying custom IP configuration..."
            mkdir -p files/etc/uci-defaults
            cat > files/etc/uci-defaults/99-custom-ip << 'EOF'
#!/bin/sh
uci set network.lan.ipaddr='*************'
uci commit network
EOF
          fi
        fi

    - name: Free up space before compile
      run: |
        # Clean up unnecessary files to free space
        sudo apt-get clean
        sudo apt-get autoremove -y --purge
        sudo rm -rf /var/lib/apt/lists/*
        sudo rm -rf /tmp/* /var/tmp/*
        sudo rm -rf /var/log/*.log /var/log/*/*.log
        # Clean docker and containers
        sudo docker system prune -af --volumes
        # Clean snap cache if exists
        sudo rm -rf /var/lib/snapd/cache/* || true
        # Clean journal logs
        sudo journalctl --vacuum-time=1d || true
        # Remove unnecessary documentation and man pages
        sudo rm -rf /usr/share/doc/* /usr/share/man/* /usr/share/info/*
        # Show available space
        df -hT

    - name: Build with Image Builder
      if: env.USE_IMAGEBUILDER == 'true'
      id: imagebuilder
      run: |
        cd imagebuilder
        echo "Building firmware with Image Builder..."
        echo "Packages: $PACKAGES"

        # Build the firmware
        make image PROFILE="$PROFILE" PACKAGES="$PACKAGES" \
          EXTRA_IMAGE_NAME="${{ env.REPO_TAG }}-$(date +"%Y.%m.%d")" \
          FILES="files"

        echo "COMPILE_STATUS=success" >> $GITHUB_ENV
        echo "DEVICE_NAME=_$PROFILE" >> $GITHUB_ENV
        echo "FILE_DATE=_$(date +"%Y%m%d%H%M")" >> $GITHUB_ENV

    - name: Compile the firmware (source build)
      if: env.USE_IMAGEBUILDER != 'true'
      id: compile
      run: |
        cd openwrt
        echo -e "$(nproc) thread compile"
        make -j$(nproc) || make -j1 || make -j1 V=s
        echo "COMPILE_STATUS=success" >> $GITHUB_ENV
        grep '^CONFIG_TARGET.*DEVICE.*=y' .config | sed -r 's/.*DEVICE_(.*)=y/\1/' > DEVICE_NAME
        [ -s DEVICE_NAME ] && echo "DEVICE_NAME=_$(cat DEVICE_NAME)" >> $GITHUB_ENV
        echo "FILE_DATE=_$(date +"%Y%m%d%H%M")" >> $GITHUB_ENV

    - name: Check space usage
      if: (!cancelled())
      run: df -hT

    - name: Upload bin directory
      uses: actions/upload-artifact@main
      if: ${{ env.COMPILE_STATUS }} == 'success' && env.UPLOAD_BIN_DIR == 'true'
      with:
        name: OpenWrt_bin${{ env.DEVICE_NAME }}${{ env.FILE_DATE }}
        path: |
          ${{ env.USE_IMAGEBUILDER == 'true' && 'imagebuilder/bin' || 'openwrt/bin' }}

    - name: Organize files
      id: organize
      if: env.UPLOAD_FIRMWARE == 'true' && !cancelled()
      run: |
        if [ "$USE_IMAGEBUILDER" == "true" ]; then
          cd imagebuilder/bin/targets/*/*
        else
          cd openwrt/bin/targets/*/*
        fi
        rm -rf packages
        echo "FIRMWARE=$PWD" >> $GITHUB_ENV
        echo "ORANIZE_STATUS=success" >> $GITHUB_ENV

    - name: Upload firmware directory
      uses: actions/upload-artifact@main
      if: ${{ env.ORANIZE_STATUS }} == 'success' && !cancelled()
      with:
        name: OpenWrt_firmware${{ env.DEVICE_NAME }}${{ env.FILE_DATE }}
        path: ${{ env.FIRMWARE }}

    - name: Create release
      id: tag
      if: env.UPLOAD_RELEASE == 'true' && !cancelled()
      run: |
        echo "RELEASE_STATUS=success" >> $GITHUB_ENV

    - name: Upload firmware to release
      uses: softprops/action-gh-release@v2
      if: ${{ env.RELEASE_STATUS }} == 'success' && !cancelled()
      env:
        GITHUB_TOKEN: ${{ github.token }}
      with:
        body_path: ${{ github.workspace }}/.github/CHANGELOG.txt
        tag_name: ${{ env.RELEASE_TAG }}
        files: ${{ env.FIRMWARE }}/*

    - name: Delete workflow runs
      uses: Mattraks/delete-workflow-runs@main
      with:
        token: ${{ github.token }}
        retain_days: 1
        keep_minimum_runs: 3

    - name: Remove old Releases
      uses: dev-drprasad/delete-older-releases@master
      if: env.UPLOAD_RELEASE == 'true' && !cancelled()
      with:
        keep_latest: 3
        delete_tags: true
      env:
        GITHUB_TOKEN: ${{ github.token }}
