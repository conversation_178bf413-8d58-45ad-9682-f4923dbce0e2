# OpenWrt x86_64 自动构建项目

基于 ImmortalWrt 源码的自动化构建项目，支持快速 Image Builder 构建和传统源码编译。
基于 [P3TERX/Actions-OpenWrt](https://github.com/P3TERX/Actions-OpenWrt) 优化改进，大幅提升构建速度。
感谢 [P3TERX](https://github.com/P3TERX) 与 [ImmortalWrt](https://github.com/immortalwrt)

## 🚀 构建优化特性

- **双构建模式**：Image Builder（5-15分钟）+ 源码编译（备选）
- **智能版本匹配**：自动获取 GitHub tag 对应的 Image Builder
- **自动回退机制**：Image Builder 不可用时自动切换到源码编译
- **优化依赖安装**：根据构建模式安装最少必要依赖
- **智能包管理**：自动从配置文件提取和优化包列表

## 📋 快速开始

### 手动触发构建
1. 进入 [Actions](../../actions) 页面
2. 选择 "Build OpenWrt (Image Builder)" workflow
3. 点击 "Run workflow"
4. 选择构建选项并启动

### 测试配置
运行配置测试脚本：
```bash
chmod +x test-config.sh
./test-config.sh
```

详细说明请查看 [构建优化文档](BUILD_OPTIMIZATION.md)

## 📁 构建产物说明

### 固件文件
- `immortalwrt-x86-64-generic-squashfs-combined-efi.img.gz` - UEFI 系统安装镜像
- `immortalwrt-x86-64-generic-squashfs-combined.img.gz` - 传统 BIOS 系统安装镜像
- `immortalwrt-x86-64-generic-squashfs-combined-efi.qcow2` - Proxmox VE 虚拟机镜像 (UEFI)
- `immortalwrt-x86-64-generic-squashfs-combined.qcow2` - Proxmox VE 虚拟机镜像 (BIOS)

### 系统组件
- `immortalwrt-x86-64-generic-kernel.bin` - 内核镜像文件
- `immortalwrt-x86-64-generic-rootfs.tar.gz` - 根文件系统归档
- `immortalwrt-x86-64-generic-squashfs-rootfs.img.gz` - 容器用根文件系统镜像

### 构建信息
- `config.buildinfo` - 构建配置信息
- `feeds.buildinfo` - Feeds 版本信息
- `immortalwrt-x86-64-generic.manifest` - 软件包清单

## ⚙️ 自定义配置

### 网络设置
默认 LAN IP: `*************`
默认用户: `root`（无密码）

### 包含的主要软件
- LuCI Web 管理界面
- OpenClash 代理工具
- SmartDNS 智能 DNS
- 网络唤醒 (WOL)
- 磁盘管理工具
- 多种网络工具和插件

## 🔧 开发说明

### 主要脚本
- `generate-packages.sh` - 包列表生成脚本
- `diy-imagebuilder.sh` - Image Builder 自定义脚本
- `diy-part1.sh` / `diy-part2.sh` - 源码编译自定义脚本
- `test-config.sh` - 配置测试脚本

### 构建时间对比
| 构建模式 | 时间 | 适用场景 |
|---------|------|---------|
| Image Builder | 5-15分钟 | 日常构建、包配置变更 |
| 源码编译 | 1-3小时 | 内核定制、深度修改 |

---

**注意**: 本项目主要用于学习和测试目的，请根据自己的需求进行适当修改。
