# OpenWrt 构建流程优化说明

## 优化概述

本项目已经优化了 GitHub Actions 构建流程，主要通过使用 **Image Builder** 替代完整源码编译来大幅提升构建速度。

## 主要改进

### 1. 双构建模式
- **Image Builder 模式**（默认）：快速构建，通常 5-15 分钟完成
- **完整源码编译模式**：传统方式，用作备选方案

### 2. 智能版本匹配
- 自动获取 GitHub tag 对应的 Image Builder
- 如果 Image Builder 不可用，自动回退到源码编译
- 确保构建版本与上游仓库标签一致

### 3. 优化的依赖安装
- Image Builder 模式只安装必要的工具包
- 减少初始化时间和磁盘占用

### 4. 智能包管理
- 自动从 `.config` 文件提取包列表
- 排除不必要的内核模块和基础包
- 支持包的启用和禁用

## 使用方法

### 手动触发构建

1. 进入 GitHub Actions 页面
2. 选择 "Build OpenWrt (Image Builder)" workflow
3. 点击 "Run workflow"
4. 选择构建选项：
   - `Clean cache`: 是否清理缓存
   - `Use full build`: 是否强制使用完整源码编译

### 构建模式选择

#### Image Builder 模式（推荐）
- **优点**：构建速度快，资源占用少
- **适用场景**：日常构建，测试配置变更
- **限制**：无法修改内核配置，包选择受限于预编译包

#### 完整源码编译模式
- **优点**：完全自定义，可修改任何配置
- **适用场景**：需要自定义内核或添加特殊补丁
- **缺点**：构建时间长（1-3小时）

## 配置文件说明

### 主要文件
- `.config`: OpenWrt 配置文件
- `generate-packages.sh`: 包列表生成脚本
- `diy-imagebuilder.sh`: Image Builder 自定义脚本
- `diy-part1.sh`: 源码编译前置脚本
- `diy-part2.sh`: 源码编译后置脚本

### 自定义配置

#### 修改网络配置
编辑 `diy-imagebuilder.sh` 中的网络配置部分：
```bash
option ipaddr '*************'  # 修改为你需要的IP
```

#### 添加自定义文件
将自定义文件放在 `files/` 目录下，构建时会自动包含。

#### 修改包列表
编辑 `.config` 文件，添加或移除包：
```
CONFIG_PACKAGE_package_name=y    # 启用包
# CONFIG_PACKAGE_package_name is not set  # 禁用包
```

## 构建时间对比

| 构建模式 | 平均时间 | 资源占用 | 自定义程度 |
|---------|---------|---------|-----------|
| Image Builder | 5-15分钟 | 低 | 中等 |
| 完整源码编译 | 1-3小时 | 高 | 完全 |

## 故障排除

### Image Builder 下载失败
- 检查网络连接
- 确认对应版本的 Image Builder 是否存在
- 系统会自动回退到源码编译

### 包依赖错误
- 检查 `.config` 中的包配置
- 确认包在对应版本中可用
- 查看构建日志中的错误信息

### 自定义文件未生效
- 确认文件路径正确
- 检查文件权限设置
- 查看 `diy-imagebuilder.sh` 脚本执行日志

## 环境变量说明

| 变量名 | 说明 | 默认值 |
|-------|------|--------|
| `REPO_NAME` | 源码仓库 | `immortalwrt/immortalwrt` |
| `BUILD_VER` | 构建版本 | `24.10` |
| `TARGET` | 目标架构 | `x86` |
| `SUBTARGET` | 子架构 | `64` |
| `PROFILE` | 设备配置 | `generic` |

## 高级用法

### 自定义 Image Builder URL
如果需要使用特定的 Image Builder，可以修改下载 URL：
```bash
IB_URL="https://your-custom-url/imagebuilder.tar.xz"
```

### 添加自定义软件源
在 `diy-imagebuilder.sh` 中添加：
```bash
echo "src/gz custom_feed http://your-feed-url" >> repositories.conf
```

## 注意事项

1. Image Builder 模式依赖于上游提供的预编译包
2. 某些高级自定义可能需要使用完整源码编译
3. 建议在重要变更前先测试 Image Builder 模式
4. 保持 `.config` 文件与目标架构一致

## 支持与反馈

如果遇到问题或有改进建议，请：
1. 查看 GitHub Actions 构建日志
2. 检查相关配置文件
3. 提交 Issue 描述问题详情
