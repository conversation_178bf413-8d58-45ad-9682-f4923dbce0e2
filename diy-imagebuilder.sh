#!/bin/bash
#
# DIY script for Image Builder customization
# This script customizes the Image Builder environment
#

echo "Starting Image Builder customization..."

# Set working directory to imagebuilder
cd imagebuilder || exit 1

# Create custom files directory if it doesn't exist
mkdir -p files

# Copy custom files from workspace
if [ -d "$GITHUB_WORKSPACE/files" ]; then
    echo "Copying custom files..."
    cp -r "$GITHUB_WORKSPACE/files"/* files/ 2>/dev/null || true
fi

# Create custom network configuration
mkdir -p files/etc/config
if [ ! -f files/etc/config/network ]; then
    echo "Creating custom network configuration..."
    cat > files/etc/config/network << 'EOF'
config interface 'loopback'
	option ifname 'lo'
	option proto 'static'
	option ipaddr '127.0.0.1'
	option netmask '*********'

config globals 'globals'
	option ula_prefix 'fd12:3456:789a::/48'

config interface 'lan'
	option type 'bridge'
	option ifname 'eth0'
	option proto 'static'
	option ipaddr '*************'
	option netmask '*************'
	option ip6assign '60'
EOF
fi

# Create custom system configuration
if [ ! -f files/etc/config/system ]; then
    echo "Creating custom system configuration..."
    cat > files/etc/config/system << 'EOF'
config system
	option hostname 'ImmortalWrt'
	option timezone 'CST-8'
	option ttylogin '0'
	option log_size '64'
	option urandom_seed '0'

config timeserver 'ntp'
	option enabled '1'
	option enable_server '0'
	list server 'ntp.aliyun.com'
	list server 'time1.cloud.tencent.com'
	list server 'time.ustc.edu.cn'
	list server 'cn.pool.ntp.org'
EOF
fi

# Create version info file
mkdir -p files/etc
echo "DISTRIB_GITHUBVER='$VERSION_INFO'" >> files/etc/openwrt_release

# Set proper permissions
find files -type f -exec chmod 644 {} \;
find files -type d -exec chmod 755 {} \;

echo "Image Builder customization completed."
