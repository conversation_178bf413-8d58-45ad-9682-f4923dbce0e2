#
# Copyright (c) 2019-2021 P3TERX <https://p3terx.com>
#
# This is free software, licensed under the MIT License.
# See /LICENSE for more information.
#
# https://github.com/P3TERX/Actions-OpenWrt
# File: .github/workflows/update-checker.yml
# Description: Source code update checker with Image Builder support
#

name: Update Checker

env:
  REPO_NAME: immortalwrt/immortalwrt
  BUILD_VER: 24.10

on:
  workflow_dispatch:
    inputs:
      force_build:
        description: 'Force build even if no updates'
        required: false
        default: "false"
  schedule:
    - cron: '23 1 * * *'

jobs:
  check:
    runs-on: ubuntu-latest

    steps:

    - name: Check Updates
      run: |
        CURRENT_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$GITHUB_REPOSITORY/releases/latest" | awk -F '"' '/tag_name/{print $4}')
        REPO_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$REPO_NAME/tags" | awk -F '"' '/name/{print $4}' | grep $BUILD_VER | head -n 1)

        if [[ $CURRENT_TAG =~ "rc" ]]; then
                CURRENT_VER=$(echo "$CURRENT_TAG" | awk -F'-' '{print $1 "-" $2}')
        else
                CURRENT_VER=$(echo "$CURRENT_TAG" | awk -F'-' '{print $1}')
        fi

        echo "Current Version: $CURRENT_VER"
        echo "Repo Version: $REPO_TAG"
        echo "CURRENT_VER=$CURRENT_VER" >> $GITHUB_ENV
        echo "REPO_TAG=$REPO_TAG" >> $GITHUB_ENV

        # Check if Image Builder is available for this version
        IB_URL="https://downloads.immortalwrt.org/releases/$BUILD_VER/targets/x86/64/immortalwrt-imagebuilder-$REPO_TAG-x86-64.Linux-x86_64.tar.xz"
        if wget -q --spider "$IB_URL"; then
          echo "Image Builder available for $REPO_TAG"
          echo "IB_AVAILABLE=true" >> $GITHUB_ENV
        else
          echo "Image Builder not available for $REPO_TAG"
          echo "IB_AVAILABLE=false" >> $GITHUB_ENV
        fi

        if [ "${{ github.event.inputs.force_build }}" == "true" ]; then
          echo "Force build requested"
          echo "SHOULD_BUILD=true" >> $GITHUB_ENV
        elif [ "$CURRENT_VER" != "$REPO_TAG" ]; then
          echo "Found Updates, Start Build..."
          echo "SHOULD_BUILD=true" >> $GITHUB_ENV
        else
          echo "No Updates"
          echo "SHOULD_BUILD=false" >> $GITHUB_ENV
        fi

    - name: Trigger build
      if: env.SHOULD_BUILD == 'true'
      uses: peter-evans/repository-dispatch@main
      with:
        token: ${{ secrets.MY_SECRETS_TOKEN }}
        event-type: Source Code Update

    - name: Delete workflow runs
      uses: Mattraks/delete-workflow-runs@main
      with:
        token: ${{ github.token }}
        retain_days: 1
        keep_minimum_runs: 3
